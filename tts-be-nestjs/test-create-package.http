### Test Create Package Endpoint
POST http://localhost:3001/orders/create-package
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "tiktokShopId": 1,
  "orderId": 123,
  "orderIdTT": "7891234567890123456",
  "orderLineItemIds": ["7123456789", "7123456790"],
  "shippingServiceId": "standard_shipping",
  "dimension": {
    "length": 10,
    "width": 8,
    "height": 5,
    "unit": "cm"
  },
  "weight": {
    "value": 1.5,
    "unit": "kg"
  }
}

### Test Create Package with Minimal Required Fields
POST http://localhost:3001/orders/create-package
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "tiktokShopId": 1,
  "orderId": 123,
  "orderIdTT": "7891234567890123456",
  "orderLineItemIds": ["7123456789", "7123456790"]
}

### Test Create Package with Invalid TikTok Shop ID (should return 404)
POST http://localhost:3001/orders/create-package
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "tiktokShopId": 999,
  "orderId": 123,
  "orderIdTT": "7891234567890123456",
  "orderLineItemIds": ["7123456789", "7123456790"]
}

### Test Create Package with Invalid Order ID (should return 404)
POST http://localhost:3001/orders/create-package
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "tiktokShopId": 1,
  "orderId": 999,
  "orderIdTT": "7891234567890123456",
  "orderLineItemIds": ["7123456789", "7123456790"]
}

### Test Create Package with Missing Required Fields (should return 400)
POST http://localhost:3001/orders/create-package
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "tiktokShopId": 1,
  "orderId": 123
}

### Test Create Package without Authentication (should return 401)
POST http://localhost:3001/orders/create-package
Content-Type: application/json

{
  "tiktokShopId": 1,
  "orderId": 123,
  "orderIdTT": "7891234567890123456",
  "orderLineItemIds": ["7123456789", "7123456790"]
}
