import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTikTokPackagesTable1754695269383 implements MigrationInterface {
    name = 'CreateTikTokPackagesTable1754695269383'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tiktok_packages" ("id" SERIAL NOT NULL, "packageIdTT" character varying, "orderIdTT" character varying, "orderId" integer NOT NULL, "orderLineItemIds" jsonb, "createTimeTT" integer, "shippingServiceInfo" jsonb, "dimension" jsonb, "weight" jsonb, "rawTikTokResponse" jsonb, "userId" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1863f35189dae9c497dc366e61d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD CONSTRAINT "FK_9ca8540233bc93e7acacf799794" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD CONSTRAINT "FK_532c485b0bcb05b1e0c9c2f72b5" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP CONSTRAINT "FK_532c485b0bcb05b1e0c9c2f72b5"`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP CONSTRAINT "FK_9ca8540233bc93e7acacf799794"`);
        await queryRunner.query(`DROP TABLE "tiktok_packages"`);
    }

}
