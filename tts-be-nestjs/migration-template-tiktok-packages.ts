import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTikTokPackagesTable1754648429864 implements MigrationInterface {
  name = 'CreateTikTokPackagesTable1754648429864';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create tiktok_packages table
    await queryRunner.query(`
      CREATE TABLE "tiktok_packages" (
        "id" SERIAL NOT NULL,
        "packageIdTT" character varying,
        "orderIdTT" character varying,
        "orderId" integer NOT NULL,
        "orderLineItemIds" jsonb,
        "createTimeTT" integer,
        "shippingServiceInfo" jsonb,
        "dimension" jsonb,
        "weight" jsonb,
        "rawTikTokResponse" jsonb,
        "userId" integer,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_tiktok_packages_id" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraint to orders table
    await queryRunner.query(`
      ALTER TABLE "tiktok_packages" 
      ADD CONSTRAINT "FK_tiktok_packages_order_id" 
      FOREIGN KEY ("orderId") 
      REFERENCES "orders"("id") 
      ON DELETE CASCADE
    `);

    // Add foreign key constraint to users table
    await queryRunner.query(`
      ALTER TABLE "tiktok_packages" 
      ADD CONSTRAINT "FK_tiktok_packages_user_id" 
      FOREIGN KEY ("userId") 
      REFERENCES "users"("id") 
      ON DELETE SET NULL
    `);

    // Create indexes for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_tiktok_packages_order_id" 
      ON "tiktok_packages" ("orderId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_tiktok_packages_user_id" 
      ON "tiktok_packages" ("userId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_tiktok_packages_package_id_tt" 
      ON "tiktok_packages" ("packageIdTT")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_tiktok_packages_order_id_tt" 
      ON "tiktok_packages" ("orderIdTT")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_tiktok_packages_order_id_tt"`);
    await queryRunner.query(`DROP INDEX "IDX_tiktok_packages_package_id_tt"`);
    await queryRunner.query(`DROP INDEX "IDX_tiktok_packages_user_id"`);
    await queryRunner.query(`DROP INDEX "IDX_tiktok_packages_order_id"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP CONSTRAINT "FK_tiktok_packages_user_id"`);
    await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP CONSTRAINT "FK_tiktok_packages_order_id"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "tiktok_packages"`);
  }
}
